# AI学习计划 - 内容地图 (MOC)

## 核心技术领域

### Claude MCP 生态
- [[Claude MCP基础]]
- [[MCP Server部署]]
- [[MCP相关概念]]
- [[Prompt写作优化]]

### API集成体系
- [[API密钥管理]]
- [[OpenAI集成]]
- [[Anthropic API]]
- [[Deepseek API]]
- [[Google API服务]]

### 知识图谱技术栈
- [[知识图谱构建]]
- [[Neo4j图数据库]]
- [[Longchain框架]]
- [[图数据可视化]]

### 自动化工具链
- [[n8n自动化平台]]
- [[工作流自动化]]
- [[Flowwith工具]]
- [[Genspark平台]]

### 可视化与界面
- [[Draw.io集成]]
- [[Claude Desktop]]
- [[智能绘图系统]]
- [[可视化展示]]

## 学习阶段规划

### 第一阶段：基础设施搭建
**核心目标**: 建立技术基础和环境
- [[API密钥管理]] → [[Claude MCP基础]]
- [[官方学习资源]] → [[MCP相关概念]]

### 第二阶段：工具探索实践
**核心目标**: 掌握各工具使用方法
- [[Claude工具体验]] ↔ [[Prompt写作优化]]
- [[Neo4j入门]] → [[知识图谱构建]]
- [[n8n自动化平台]] → [[工作流自动化]]

### 第三阶段：集成应用开发
**核心目标**: 构建完整解决方案
- [[MCP Server部署]] → [[自定义功能开发]]
- [[Claude Draw.io集成]] → [[智能绘图系统]]
- [[知识图谱构建]] → [[图数据可视化]]

## 技术依赖关系

### 基础依赖链
```
API密钥管理 → Claude MCP基础 → MCP Server部署
```

### 知识图谱技术栈
```
Neo4j图数据库 ← 知识图谱构建 → Longchain框架
                     ↓
                图数据可视化
```

### 自动化集成链
```
n8n自动化平台 → API集成 → 工作流自动化
```

### 可视化应用链
```
Claude MCP基础 → Draw.io集成 → 智能绘图系统
```

## 实践项目规划
- [[个人MCP服务器项目]]
- [[知识图谱原型系统]]
- [[AI辅助绘图工具]]
- [[自动化工作流系统]]

## 学习资源中心
- [[小红书学习资源]]
- [[官方文档资源]]
- [[社区学习平台]]

---
**标签**: #AI学习 #MOC #技术栈 #学习规划
