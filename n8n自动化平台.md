# n8n自动化平台

## 关联笔记
- [[AI学习计划_MOC]] - 主索引
- [[工作流自动化]] - 应用领域
- [[API密钥管理]] - 集成前提
- [[Claude MCP基础]] - AI增强
- [[知识图谱构建]] - 数据流处理
- [[Flowwith工具]] - 类似工具

## 核心概念
n8n是一个开源的工作流自动化工具，通过可视化界面连接不同的服务和API，实现复杂的自动化流程。

## 主要特性
- **可视化编辑**: 拖拽式流程设计
- **丰富集成**: 支持数百种服务
- **自定义节点**: 可扩展功能
- **开源免费**: 可自部署

## 核心应用场景
1. **API数据流处理**
   - 多服务数据同步
   - 数据转换和清洗
   - 实时数据处理

2. **AI工作流集成**
   - Claude API调用自动化
   - 批量内容处理
   - 智能数据分析

3. **知识管理自动化**
   - 自动内容收集
   - 知识图谱数据更新
   - 文档生成和分发

## 技术集成点
```
API密钥管理 → n8n配置 → 自动化流程
Claude MCP → n8n节点 → 智能处理
数据源 → n8n处理 → 知识图谱更新
```

## 与其他工具的关系
- [[Claude MCP基础]] - AI能力增强
- [[知识图谱构建]] - 数据流自动化
- [[API密钥管理]] - 服务认证
- [[工作流自动化]] - 实现平台

## 学习路径
1. **基础操作** (1-2天)
   - 界面熟悉
   - 基本节点使用
   - 简单流程创建

2. **API集成** (3-5天)
   - 各种API连接
   - 数据转换处理
   - 错误处理机制

3. **高级应用** (持续)
   - 复杂流程设计
   - 自定义节点开发
   - 性能优化

## 实践项目
- 自动化内容收集系统
- AI辅助数据处理流程
- 知识图谱自动更新系统

## 替代工具对比
- [[Flowwith工具]] - 类似功能
- [[Genspark平台]] - 相关平台
- Zapier - 商业化方案

---
**优先级**: 中高
**学习难度**: 中等
**标签**: #自动化 #工作流 #API集成 #n8n
