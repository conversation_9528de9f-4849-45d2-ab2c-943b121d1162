# 小红书学习资源

## 关联笔记
- [[AI学习计划_MOC]] - 主索引
- [[<PERSON> MCP基础]] - 主要学习内容
- [[Prompt写作优化]] - 技能提升
- [[MCP相关概念]] - 概念理解
- [[社区学习平台]] - 其他平台

## 核心资源链接

### Claude MCP相关
1. **Claude自定义命令基础**
   - 链接: `http://xhslink.com/a/o3BDQ0OMLdmgb`
   - 内容: MCP基础概念和入门
   - 关联: [[<PERSON> MCP基础]]

2. **Claude自定义命令进阶**
   - 链接: `http://xhslink.com/a/Ez9MEVZf2dmgb`
   - 内容: 高级MCP功能和应用
   - 关联: [[Claude MCP基础]]

### Prompt写作技巧
3. **Prompt写作基础**
   - 链接: `http://xhslink.com/a/mmhUxDwK6bmgb`
   - 内容: 基础Prompt编写技巧
   - 关联: [[Prompt写作优化]]

4. **Prompt写作进阶**
   - 链接: `http://xhslink.com/a/M2n2X9zfQemgb`
   - 内容: 高级Prompt优化方法
   - 关联: [[Prompt写作优化]]

### MCP生态系统
5. **MCP相关概念详解**
   - 链接: `https://www.xiaohongshu.com/explore/684d6643000000002301c734?note_flow_source=wechat&xsec_token=CB-GlZhfjAtzacvycwanKZp7uIe8-6Fn5VbrjwsOrhhh4=`
   - 内容: Zen安装、Content7、Deekwiki等概念
   - 关联: [[MCP相关概念]]

## 学习策略
### 阅读顺序
1. Claude MCP基础资源 (1-2)
2. MCP相关概念 (5)
3. Prompt写作资源 (3-4)

### 实践建议
- 边学边练，及时验证
- 记录关键要点和疑问
- 结合实际项目应用
- 与其他资源交叉验证

## 内容分类
### 基础入门
- Claude MCP基础概念
- 基本命令使用
- 环境搭建指南

### 技能提升
- Prompt优化技巧
- 高级功能应用
- 最佳实践分享

### 生态理解
- 相关工具介绍
- 集成方案探讨
- 社区经验分享

## 学习笔记模板
```markdown
## 资源信息
- 标题: 
- 链接: 
- 学习日期: 

## 核心要点
- 要点1
- 要点2
- 要点3

## 实践验证
- 尝试内容
- 遇到问题
- 解决方案

## 关联思考
- 与其他知识的联系
- 应用场景思考
- 后续学习方向
```

## 补充资源方向
- 官方文档对比验证
- 英文原版资源查找
- 技术社区讨论参与
- 实践项目案例收集

## 资源评估
- **权威性**: 来源可靠性
- **时效性**: 内容更新程度
- **实用性**: 实际应用价值
- **完整性**: 内容覆盖范围

---
**资源类型**: 社交媒体学习
**更新频率**: 持续关注
**标签**: #小红书 #学习资源 #Claude #MCP #Prompt
