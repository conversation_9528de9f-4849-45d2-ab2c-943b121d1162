# API密钥管理

## 关联笔记
- [[AI学习计划_MOC]] - 主索引
- [[Claude MCP基础]] - 需要Anthropic API
- [[OpenAI集成]] - 需要OpenAI API
- [[Deepseek API]] - 需要Deepseek API
- [[Google API服务]] - 需要Google API
- [[安全最佳实践]] - 密钥安全管理

## 核心概念
API密钥是访问各种AI服务的基础凭证，是整个学习计划的前置条件。

## 需要获取的API密钥

### 高优先级
1. **Google API Key**
   - 状态：需确认和整理现有密钥
   - 用途：搜索、地图、翻译服务
   - 关联：[[Google API服务]]

2. **Anthropic API Key**
   - 状态：待获取
   - 用途：Claude模型调用
   - 关联：[[Claude MCP基础]]、[[Anthropic API]]

3. **OpenAI API Key**
   - 状态：待获取
   - 用途：GPT模型调用
   - 关联：[[OpenAI集成]]

4. **Deepseek API Key**
   - 状态：待获取
   - 用途：Deepseek模型调用
   - 关联：[[Deepseek API]]

## 管理策略
- 环境变量存储
- 定期密钥轮换
- 使用量监控
- 费用控制设置

## 安全原则
- 不在代码中硬编码
- .env文件管理
- 访问权限控制
- 使用限额设置

## 行动清单
- [ ] 整理Google API Key
- [ ] 注册Anthropic账户
- [ ] 注册OpenAI账户
- [ ] 注册Deepseek账户
- [ ] 建立密钥管理系统

---
**优先级**: 极高
**依赖关系**: 所有API集成的前置条件
**标签**: #API #密钥 #安全 #基础设施
