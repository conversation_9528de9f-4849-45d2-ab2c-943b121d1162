# Prompt写作优化

## 关联笔记
- [[AI学习计划_MOC]] - 主索引
- [[<PERSON> MCP基础]] - 应用平台
- [[Claude工具体验]] - 实践环境
- [[小红书学习资源]] - 学习材料

## 核心概念
Prompt工程是与AI模型有效交互的关键技能，通过优化提示词来获得更准确、更有用的AI响应。

## 学习资源
**小红书链接**:
- `http://xhslink.com/a/mmhUxDwK6bmgb` - Prompt写作基础
- `http://xhslink.com/a/M2n2X9zfQemgb` - Prompt写作进阶

## 核心原则
1. **明确性**: 清晰表达需求和期望
2. **具体性**: 提供具体的上下文和示例
3. **结构化**: 使用逻辑清晰的结构
4. **迭代优化**: 根据结果持续改进

## Prompt结构模板
```
角色设定 + 任务描述 + 上下文信息 + 输出要求 + 示例参考
```

## 优化技巧
### 基础技巧
- 使用具体的动词和名词
- 提供清晰的背景信息
- 明确输出格式要求
- 包含相关示例

### 高级技巧
- 链式思维 (Chain of Thought)
- 角色扮演 (Role Playing)
- 分步骤引导
- 反向验证

## 应用场景
### Claude MCP增强
- 自定义命令优化
- 功能描述精准化
- 错误处理改进

### 知识工作
- 文档分析和总结
- 创意内容生成
- 问题解决方案

### 自动化流程
- [[n8n自动化平台]] 中的AI节点
- [[工作流自动化]] 的智能决策
- 批量内容处理

## 实践平台
- [[Claude工具体验]] - claude.ia
- [[Claude Desktop]] - 桌面应用
- [[个人MCP服务器项目]] - 自定义环境

## 评估标准
1. **准确性**: 输出是否符合预期
2. **相关性**: 内容是否切题
3. **完整性**: 是否覆盖所有要求
4. **可用性**: 结果是否可直接使用

## 迭代流程
1. **初始Prompt** - 基础版本
2. **结果分析** - 识别问题
3. **Prompt调整** - 针对性改进
4. **效果验证** - 测试新版本
5. **持续优化** - 循环改进

## 最佳实践
- 保存有效的Prompt模板
- 建立个人Prompt库
- 定期回顾和更新
- 与他人分享经验

## 进阶方向
- [[自定义功能开发]] - MCP命令优化
- [[智能绘图系统]] - 可视化Prompt
- [[知识图谱构建]] - 结构化提示

---
**优先级**: 高
**学习周期**: 持续优化
**标签**: #Prompt #AI交互 #优化技巧 #实践技能
