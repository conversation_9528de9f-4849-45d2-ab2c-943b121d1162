# Claude MCP基础

## 关联笔记
- [[AI学习计划_MOC]] - 主索引
- [[API密钥管理]] - 前置依赖
- [[MCP Server部署]] - 进阶应用
- [[MCP相关概念]] - 相关技术
- [[Prompt写作优化]] - 应用场景
- [[Claude工具体验]] - 实践平台

## 核心概念
MCP (Model Context Protocol) 是Anthropic开发的协议，用于扩展Claude的功能和上下文理解能力，是整个Claude生态的核心技术。

## 关键学习资源
**小红书资源**:
- `http://xhslink.com/a/o3BDQ0OMLdmgb` - 自定义命令基础
- `http://xhslink.com/a/Ez9MEVZf2dmgb` - 自定义命令进阶

## 核心命令系统
- `Claude mcp` - 基础MCP命令
- `Claude mcp add` - 添加MCP功能
- 自定义命令创建和管理

## 技术特性
- **上下文扩展**: 增强特定领域理解
- **功能集成**: 连接外部工具和服务
- **自定义命令**: 扩展Claude功能边界
- **协议标准**: 统一的扩展接口

## 应用场景
- 专业领域知识增强 → [[知识图谱构建]]
- 工具链集成 → [[工作流自动化]]
- 个性化AI助手开发
- 自动化流程构建 → [[n8n自动化平台]]

## 技术依赖关系
```
API密钥管理 → Claude MCP基础 → MCP Server部署
                    ↓
              Prompt写作优化
                    ↓
              Claude工具体验
```

## 学习路径
1. **理论基础** (1-2天)
   - 阅读小红书资源
   - 理解MCP协议原理
   
2. **命令实践** (2-3天)
   - 掌握基础命令
   - 创建简单自定义命令
   
3. **集成应用** (持续)
   - 与其他工具集成
   - 构建复杂工作流

## 进阶方向
- [[MCP Server部署]] - 自建服务器
- [[Claude Draw.io集成]] - 可视化集成
- [[自定义功能开发]] - 高级定制

---
**优先级**: 极高
**学习周期**: 1-2周基础，持续深入
**标签**: #Claude #MCP #自定义命令 #协议
